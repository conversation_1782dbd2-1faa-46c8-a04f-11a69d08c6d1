import { Request, Response } from "express";
import { AppDataSource } from "../Config/db";
import { Product } from "../Models/Product";
import { Stock } from "../Models/Stock";
import { Branch } from "../Models/Branch";
import { Like } from "typeorm";

interface ProductInput {
  product_code: string;
  // เ่่ม์ลด์อื่น ๆ ตาม schema ใช้
  [key: string]: any;
}

export class ProductController {
  public async getAll(req: Request, res: Response): Promise<void> {
    try {
      const productRepository = AppDataSource.getRepository(Product);
      const products = await productRepository.find({
        relations: [
          "manufacturer",
          "distributor",
          "product_group",
          "special_report_group",
        ],
      });
      res.status(200).json(products);
    } catch (error) {
      res.status(500).json({ message: "Server error" });
    }
  }

  public async getAllByFilter(req: Request, res: Response): Promise<void> {
    const { search, filter } = req.body;

    try {
      const productRepository = AppDataSource.getRepository(Product);

      const query = productRepository
        .createQueryBuilder("product")
        .leftJoinAndSelect("product.manufacturer", "manufacturer")
        .leftJoinAndSelect("product.distributor", "distributor")
        .leftJoinAndSelect("product.product_group", "product_group")
        .leftJoinAndSelect(
          "product.special_report_group",
          "special_report_group"
        )
        .where("1=1");

      if (search) {
        query.andWhere(
          `(
            product.product_code LIKE :search OR
            product.product_name LIKE :search OR
            product.unit LIKE :search OR
            product.barcode LIKE :search OR
            product.generic_group LIKE :search OR
            product.storage_location LIKE :search
          )`,
          { search: `%${search}%` }
        );
      }

      if (filter && search) {
        query.andWhere(`product.${filter} LIKE :search`, {
          search: `%${search}%`,
        });
      }

      const products = await query.getMany();
      res.status(200).json(products);
    } catch (error) {
      console.error("Error filtering products:", error);
      res.status(500).json({ message: "Server error", error });
    }
  }

  public async getById(req: Request, res: Response): Promise<void> {
    const { id } = req.params;
    try {
      const productRepository = AppDataSource.getRepository(Product);
      const product = await productRepository.findOne({
        where: { id: Number(id) },
        relations: [
          "manufacturer",
          "distributor",
          "product_group",
          "special_report_group",
        ],
      });
      if (product) {
        res.status(200).json(product);
      } else {
        res.status(404).json({ message: "Product not found" });
      }
    } catch (error) {
      res.status(500).json({ message: "Server error" });
    }
  }

  public async create(req: Request, res: Response): Promise<void> {
    const productRepository = AppDataSource.getRepository(Product);
    const stockRepository = AppDataSource.getRepository(Stock);
    const branchRepository = AppDataSource.getRepository(Branch);

    try {
      const { product_code } = req.body;

      const existingProduct = await productRepository.findOne({
        where: { product_code },
      });
      if (existingProduct) {
        res
          .status(400)
          .json({
            message:
              "Product code already exists. Please use a different code.",
          });
        return;
      }

      const branches = await branchRepository.find();
      if (branches.length === 0) {
        res.status(400).json({ message: "No branches available" });
        return;
      }

      const newProduct = productRepository.create(req.body);
      const savedProduct = await productRepository.save(newProduct);

      if (Array.isArray(savedProduct)) {
        res.status(500).json({ message: "Error saving Product" });
        return;
      }

      const stockEntries = branches.map((branch) => {
        const stock = new Stock();
        stock.product = savedProduct;
        stock.branch = branch;
        stock.remaining = 0;
        stock.status = "ค้าหมด";
        return stock;
      });

      await stockRepository.save(stockEntries);

      res.status(201).json({ product: savedProduct });
    } catch (error) {
      console.error("Error creating Product and Stock:", error);
      res.status(400).json({ message: "Error creating Product", error });
    }
  }

  public async createBulk(req: Request, res: Response): Promise<void> {
    const productRepository = AppDataSource.getRepository(Product);
    const stockRepository = AppDataSource.getRepository(Stock);
    const branchRepository = AppDataSource.getRepository(Branch);

    try {
      const productsData = req.body as ProductInput[];

      const branches = await branchRepository.find();
      if (branches.length === 0) {
        res.status(400).json({ message: "No branches available" });
        return;
      }

      const productCodes = productsData.map(
        (p: ProductInput) => p.product_code
      );
      const existingProducts = await productRepository.find({
        where: productCodes.map((code: string) => ({ product_code: code })),
      });

      const existingCodes = new Set(
        existingProducts.map((p) => p.product_code)
      );
      const duplicates = productsData.filter((p) =>
        existingCodes.has(p.product_code)
      );

      if (duplicates.length > 0) {
        res.status(400).json({
          message: "Some product codes already exist.",
          duplicates: duplicates.map((p) => p.product_code),
        });
        return;
      }

      const newProducts = productRepository.create(productsData);
      const savedProducts = await productRepository.save(newProducts);

      const stockEntries = [];
      for (const product of savedProducts) {
        for (const branch of branches) {
          const stock = new Stock();
          stock.product = product;
          stock.branch = branch;
          stock.remaining = 0;
          stock.status = "ค้าหมด";
          stockEntries.push(stock);
        }
      }
      await stockRepository.save(stockEntries);

      res
        .status(201)
        .json({
          message: "Bulk products created",
          count: savedProducts.length,
        });
    } catch (error) {
      console.error("Error creating bulk products:", error);
      res.status(500).json({ message: "Error creating bulk products", error });
    }
  }

  public async update(req: Request, res: Response): Promise<void> {
    const { id } = req.params;
    const productRepository = AppDataSource.getRepository(Product);

    try {
      const product = await productRepository.findOne({
        where: { id: Number(id) },
      });
      if (product) {
        productRepository.merge(product, req.body);
        const updatedProduct = await productRepository.save(product);
        res.status(200).json(updatedProduct);
      } else {
        res.status(404).json({ message: "Product not found" });
      }
    } catch (error) {
      res.status(400).json({ message: "Error updating Product" });
    }
  }

  public async delete(req: Request, res: Response): Promise<void> {
    const { id } = req.params;
    const productRepository = AppDataSource.getRepository(Product);

    try {
      const result = await productRepository.delete(id);
      if (result.affected) {
        res.status(204).send();
      } else {
        res.status(404).json({ message: "Product not found" });
      }
    } catch (error) {
      res.status(500).json({ message: "Server error" });
    }
  }

  public async getByName(req: Request, res: Response): Promise<void> {
    const { name } = req.params;

    try {
      const productRepository = AppDataSource.getRepository(Product);
      const products = await productRepository.find({
        where: { product_name: Like(`%${name}%`) },
        relations: [
          "manufacturer",
          "distributor",
          "product_group",
          "special_report_group",
        ],
      });

      if (products.length > 0) {
        res.status(200).json(products);
      } else {
        res.status(404).json({ message: "No products found with that name" });
      }
    } catch (error) {
      console.error("Error finding products by name:", error);
      res.status(500).json({ message: "Server error" });
    }
  }

  public async getExpirationAlerts(req: Request, res: Response): Promise<void> {
    try {
      const productRepository = AppDataSource.getRepository(Product);
      const currentDate = new Date();
      const threeMonthsFromNow = new Date();
      threeMonthsFromNow.setMonth(currentDate.getMonth() + 3);

      // Get all products with expiration dates
      const products = await productRepository.find({
        relations: [
          "manufacturer",
          "distributor",
          "product_group",
          "special_report_group",
        ],
      });

      const expirationAlerts = products
        .filter((product) => product.expirationDate)
        .map((product) => {
          try {
            // Handle different date formats and ensure valid date parsing
            let expirationDate: Date;
            
            if (typeof product.expirationDate === 'string') {
              // Try to parse the date string
              expirationDate = new Date(product.expirationDate);
              
              // Check if the date is valid
              if (isNaN(expirationDate.getTime())) {
                console.warn(`Invalid expiration date for product ${product.product_code}: ${product.expirationDate}`);
                return null;
              }
            } else {
              console.warn(`Expiration date is not a string for product ${product.product_code}:`, product.expirationDate);
              return null;
            }

            const isExpired = expirationDate < currentDate;
            const isNearExpiration = !isExpired && expirationDate <= threeMonthsFromNow;

            return {
              ...product,
              isExpired,
              isNearExpiration,
              daysUntilExpiration: Math.ceil(
                (expirationDate.getTime() - currentDate.getTime()) /
                  (1000 * 60 * 60 * 24)
              ),
            };
          } catch (dateError) {
            console.error(`Error parsing expiration date for product ${product.product_code}:`, dateError);
            return null;
          }
        })
        .filter((product) => product !== null && (product.isExpired || product.isNearExpiration));

      res.status(200).json(expirationAlerts);
    } catch (error) {
      console.error("Error fetching expiration alerts:", error);
      res.status(500).json({ 
        message: "Server error", 
        error: error instanceof Error ? error.message : 'Unknown error' 
      });
    }
  }
}
