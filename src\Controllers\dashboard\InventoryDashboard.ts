import { Request, Response } from "express";
import { AppDataSource } from "../../Config/db";
import { Stock } from "../../Models/Stock";
import { Product } from "../../Models/Product";
import { Branch } from "../../Models/Branch";

export class InventoryDashboardController {
  // GET /api/inventory/by-branch
  public async getStockByBranch(req: Request, res: Response): Promise<void> {
    try {
      const stockRepository = AppDataSource.getRepository(Stock);
      
      const result = await stockRepository
        .createQueryBuilder("stock")
        .leftJoinAndSelect("stock.branch", "branch")
        .leftJoinAndSelect("stock.product", "product")
        .select([
          "branch.id as branch_id",
          "branch.name as branch_name",
          "SUM(stock.remaining) as total_remaining",
          "COUNT(DISTINCT stock.product) as product_count",
          "SUM(stock.remaining * product.standard_cost) as total_value"
        ])
        .groupBy("branch.id, branch.name")
        .getRawMany();

      res.status(200).json(result);
    } catch (error) {
      res.status(500).json({ message: "Server error", error });
    }
  }

  // GET /api/inventory/top-remaining-products
  public async getTopRemainingProducts(req: Request, res: Response): Promise<void> {
    try {
      const { limit = 10, order = 'desc' } = req.query;
      const stockRepository = AppDataSource.getRepository(Stock);
      
      const result = await stockRepository
        .createQueryBuilder("stock")
        .leftJoinAndSelect("stock.product", "product")
        .leftJoinAndSelect("stock.branch", "branch")
        .select([
          "product.id as product_id",
          "product.product_code as product_code",
          "product.product_name as product_name",
          "SUM(stock.remaining) as total_remaining",
          "SUM(stock.remaining * product.standard_cost) as total_value"
        ])
        .groupBy("product.id, product.product_code, product.product_name")
        .orderBy("total_remaining", order === 'desc' ? 'DESC' : 'ASC')
        .limit(Number(limit))
        .getRawMany();

      res.status(200).json(result);
    } catch (error) {
      res.status(500).json({ message: "Server error", error });
    }
  }

  // GET /api/inventory/value-by-group
  public async getValueByGroup(req: Request, res: Response): Promise<void> {
    try {
      const stockRepository = AppDataSource.getRepository(Stock);
      
      const result = await stockRepository
        .createQueryBuilder("stock")
        .leftJoinAndSelect("stock.product", "product")
        .leftJoinAndSelect("product.product_group", "product_group")
        .select([
          "product_group.id as group_id",
          "product_group.name as group_name",
          "SUM(stock.remaining) as total_quantity",
          "SUM(stock.remaining * product.standard_cost) as total_value",
          "COUNT(DISTINCT product.id) as product_count"
        ])
        .where("product_group.id IS NOT NULL")
        .groupBy("product_group.id, product_group.name")
        .orderBy("total_value", "DESC")
        .getRawMany();

      res.status(200).json(result);
    } catch (error) {
      res.status(500).json({ message: "Server error", error });
    }
  }
}