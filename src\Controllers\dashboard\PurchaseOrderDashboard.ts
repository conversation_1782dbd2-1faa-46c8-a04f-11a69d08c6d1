import { Request, Response } from "express";
import { AppDataSource } from "../../Config/db";
import { PurchaseOrder } from "../../Models/PurchaseOrder";
import { PurchaseOrderItem } from "../../Models/PurchaseOrderItem";

export class PurchaseOrderDashboardController {
  // GET /api/purchase-orders/summary-by-month
  public async getSummaryByMonth(req: Request, res: Response): Promise<void> {
    try {
      const poRepository = AppDataSource.getRepository(PurchaseOrder);
      
      const result = await poRepository
  .createQueryBuilder("po")
  .leftJoin("purchase_order_item", "items", "po.id = items.purchaseOrderId")
  .select([
    "strftime('%Y-%m', po.date) AS month",
    "COUNT(DISTINCT po.id) AS order_count",
    "SUM(items.quantity * items.unit_price) AS total_value",
    "SUM(items.quantity) AS total_quantity"
  ])
  .groupBy("strftime('%Y-%m', po.date)")
  .orderBy("month", "ASC")
  .getRawMany();


      res.status(200).json(result);
    } catch (error) {
      res.status(500).json({ message: "Server error", error });
    }
  }

  // GET /api/purchase-orders/top-products
  public async getTopProducts(req: Request, res: Response): Promise<void> {
    try {
      const { limit = 5 } = req.query;
      const poItemRepository = AppDataSource.getRepository(PurchaseOrderItem);
      
      const result = await poItemRepository
        .createQueryBuilder("item")
        .leftJoinAndSelect("item.product", "product")
        .select([
          "product.id as product_id",
          "product.product_code as product_code",
          "product.product_name as product_name",
          "SUM(item.quantity) as total_ordered",
          "SUM(item.quantity * item.unit_price) as total_value",
          "COUNT(DISTINCT item.purchase_order) as order_frequency"
        ])
        .groupBy("product.id, product.product_code, product.product_name")
        .orderBy("total_value", "DESC")
        .limit(Number(limit))
        .getRawMany();

      res.status(200).json(result);
    } catch (error) {
      res.status(500).json({ message: "Server error", error });
    }
  }

  // GET /api/purchase-orders/supplier-ratio
  public async getSupplierRatio(req: Request, res: Response): Promise<void> {
    try {
      const poRepository = AppDataSource.getRepository(PurchaseOrder);
      
      const result = await poRepository
  .createQueryBuilder("po")
  .leftJoin("supplier", "supplier", "po.supplierId = supplier.id")
  .leftJoin("purchase_order_item", "items", "po.id = items.purchaseOrderId")
  .select([
    "supplier.id AS supplier_id",
    "supplier.name AS supplier_name",
    "COUNT(DISTINCT po.id) AS order_count",
    "SUM(items.quantity * items.unit_price) AS total_value",
    `(COUNT(DISTINCT po.id) * 100.0 / (SELECT COUNT(*) FROM purchase_order)) AS percentage`
  ])
  .groupBy("supplier.id, supplier.name")
  .orderBy("order_count", "DESC")
  .getRawMany();


      res.status(200).json(result);
    } catch (error) {
      res.status(500).json({ message: "Server error", error });
    }
  }
}