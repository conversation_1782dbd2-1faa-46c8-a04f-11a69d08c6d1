import { Request, Response } from "express";
import { AppDataSource } from "../../Config/db";

export class PerformanceDashboardController {
  // GET /api/performance/top-on-time-suppliers
  public async getTopOnTimeSuppliers(req: Request, res: Response): Promise<void> {
    try {
      const query = `
  SELECT 
    s.id as supplier_id,
    s.name as supplier_name,
    COUNT(DISTINCT po.id) as total_orders,
    COUNT(DISTINCT gr.id) as delivered_orders,
    ROUND(COUNT(DISTINCT gr.id) * 100.0 / COUNT(DISTINCT po.id), 2) as on_time_ratio,
    ROUND(AVG(julianday(gr.receive_date) - julianday(po.date)), 2) as avg_delivery_days
  FROM supplier s
  LEFT JOIN purchase_order po ON s.id = po.supplierId
  LEFT JOIN goods_receipt gr 
    ON po.id = gr.poId 
    AND julianday(gr.receive_date) <= julianday(po.date) + 7
  GROUP BY s.id, s.name
  HAVING total_orders > 0
  ORDER BY on_time_ratio DESC, avg_delivery_days ASC
`;

      
      const result = await AppDataSource.query(query);
      res.status(200).json(result);
    } catch (error) {
      res.status(500).json({ message: "Server error", error });
    }
  }

  // GET /api/performance/slow-products
  public async getSlowProducts(req: Request, res: Response): Promise<void> {
    try {
      const query = `
  SELECT 
    p.id AS product_id,
    p.product_code,
    p.product_name,
    SUM(poi.quantity) AS total_ordered,
    COUNT(DISTINCT po.id) AS order_frequency,
    ROUND(AVG(julianday(gr.receive_date) - julianday(po.date)), 2) AS avg_receiving_days,
    COUNT(DISTINCT gr.id) AS received_count
  FROM product p
  LEFT JOIN purchase_order_item poi ON p.id = poi.productId
  LEFT JOIN purchase_order po ON poi.purchaseOrderId = po.id
  LEFT JOIN goods_receipt gr ON po.id = gr.poId
  WHERE poi.quantity > 0
    AND gr.receive_date IS NOT NULL
  GROUP BY p.id, p.product_code, p.product_name
  HAVING avg_receiving_days > 7
  ORDER BY avg_receiving_days DESC, total_ordered DESC
`;


      
      const result = await AppDataSource.query(query);
      res.status(200).json(result);
    } catch (error) {
      res.status(500).json({ message: "Server error", error });
    }
  }

  // GET /api/performance/supplier-efficiency
  public async getSupplierEfficiency(req: Request, res: Response): Promise<void> {
    try {
      const query = `
  SELECT 
    s.id as supplier_id,
    s.name as supplier_name,
    COUNT(DISTINCT po.id) as total_orders,
    COUNT(DISTINCT gr.id) as completed_deliveries,
    ROUND(AVG(julianday(gr.receive_date) - julianday(po.date)), 2) as avg_delivery_days,
    ROUND(MIN(julianday(gr.receive_date) - julianday(po.date)), 2) as fastest_delivery,
    ROUND(MAX(julianday(gr.receive_date) - julianday(po.date)), 2) as slowest_delivery,
    CASE 
      WHEN AVG(julianday(gr.receive_date) - julianday(po.date)) <= 3 THEN 'Excellent'
      WHEN AVG(julianday(gr.receive_date) - julianday(po.date)) <= 7 THEN 'Good'
      WHEN AVG(julianday(gr.receive_date) - julianday(po.date)) <= 14 THEN 'Average'
      ELSE 'Poor'
    END as efficiency_rating
  FROM supplier s
  LEFT JOIN purchase_order po ON s.id = po.supplierId
  LEFT JOIN goods_receipt gr ON po.id = gr.poId
  WHERE gr.receive_date IS NOT NULL
  GROUP BY s.id, s.name
  ORDER BY avg_delivery_days ASC
`;

      
      const result = await AppDataSource.query(query);
      res.status(200).json(result);
    } catch (error) {
      res.status(500).json({ message: "Server error", error });
    }
  }
}